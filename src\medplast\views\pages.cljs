(ns medplast.views.pages
  (:require
   [medplast.state :as state]
   [medplast.views.forms.login :as forms-login]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.firebase :as firebase]
   [medplast.firestore :as firestore]
   [reitit.frontend.easy :as rfe]
   [reagent.core :as r]))

(defn get-route-matched-view []
  (when-let [match @state/route-match]
    (if-let [view (:view (:data match))]
      [view match]
      [:span "404"])))

(defn text-logo []
  [views-core/href-link
   (rfe/href :medplast.routes/frontpage)
   [:h1 {:class ""}
    "Medplast Baltic"]])

(defn connectivity-status []
  (let [connectivity-atom @(r/track state/get-connectivity-atom)
        is-offline (not (:online @connectivity-atom))]
    (when is-offline
      [:div {:class "text-red-600 text-sm font-medium bg-red-50 px-2 py-1 rounded border border-red-200"}
       "Nėra interneto ryšio"])))

(defn login-controls [{:keys [divider]}]
  (let [user-atom @(r/track state/get-logged-in-user-atom)
        user @user-atom
        divider (or divider nil)]
    (when-let [_is-logged-in (some? user)]
      [:<>
       divider
       [:div {:class (str views-core/centering-inline-block-with-min-h " self-end ")}
        [views-core/rich-user-label user]]
       divider
       [:div {:class "text-nowrap"}
        [views-core/render-link
         {:href (rfe/href :medplast.routes/logout)
          :text "Atsijungti"}]]])))

(defn desktop-top-bar []
  (let [divider [:span {:class "opacity-40"} "\u2219"]]
    [:div {:class (str
                   "flex flex-row [word-break:break-word]"
                   " opacity-100 ml-0 gap-4 items-center")}
     [text-logo]
     [login-controls {:divider divider}]]))

(defn burger [{:keys [active on-click]}]
  (let [active-class (when active " tham-active ")]
    [:div {:class (str "z-[201] tham tham-e-squeeze tham-w-8" active-class "")
           :on-click on-click}
     [:div {:class "tham-box"}
      [:div {:class "tham-inner"}]]]))

(defonce burger-active-atom (r/atom false))

(defn mobile-top-bar []
  [:div {:class (str
                 "flex flex-row [word-break:break-word]"
                 " opacity-100 ml-0 gap-4 items-center w-full"
                 ; " z-[201]"
                 )}
   [:div {:class "flex-1"}
    [text-logo]]
   (let [user-atom @(r/track state/get-logged-in-user-atom)]
     (when-let [_is-logged-in (some? @user-atom)]
       [burger
        {:on-click #(swap! burger-active-atom not)
         :active @burger-active-atom}]))])

(def mobile-wide (< (.-innerWidth js/window) 640))

(defn top-bar []
  (if mobile-wide
    [mobile-top-bar]
    [desktop-top-bar]))

(defn loading-page []
  [:span {:class "opacity-50"} "Kraunama autentifikavimo būsena"])

(defn main-navigation-menu []
  (when-let [user @@(r/track state/get-logged-in-user-atom)]
    (let [main-links
          [{:href (rfe/href
                   :medplast.routes/patient-list
                   {:user (:uid user)})
            :text "Pacientai"}
           {:href (rfe/href
                   :medplast.routes/sale-list
                   {:user (:uid user)})
            :text "Pardavimai"}
           {:href (rfe/href :medplast.routes/product-list) :text "Produktai"}
           {:href (rfe/href :medplast.routes/user-list) :admin-only true :text "Vartotojai"}
           {:href (rfe/href :medplast.routes/statistics) :admin-only true :text "Statistika"}
           {:href (rfe/href :medplast.routes/audit-log) :admin-only true :text "Pakeitimai"}]

          utility-links
          [{:href (rfe/href :medplast.routes/user-password-reset {:user (:uid user)})
            :when-fn #(not (:admin user))
            :text "Pakeisti slaptažodį"
            :utility-style true}]

          main-links (filter (partial views-core/should-render-link user) main-links)
          utility-links (filter (partial views-core/should-render-link user) utility-links)]
      [:<>
       [:ol {:class "flex flex-col gap-4"}
        (for [link main-links]
          ^{:key (:href link)} [:li {:class ""} (views-core/render-navigation-link link)])]

       ;; Only render utility section if there are utility links to show
       (when (seq utility-links)
         [:<>
          [:div {:class "border-t border-gray-300 my-6 w-3/4"}]
          [:ol {:class "flex flex-col gap-4"}
           (for [link utility-links]
             ^{:key (:href link)} [:li {:class ""} (views-core/render-navigation-link link)])]])])))

(defn frontpage []
  [:<>
   [header "Pradinis"]
   [main-navigation-menu]])

(defn opacity-screen []
  (let [active-class (if @burger-active-atom "" "hidden")
        classes (str
                 " absolute top-0 left-0 right-0 h-full "
                 " bg-white z-[200] opacity-85 "
                 active-class)]
    [:div {:class classes}]))

(defn burger-menu []
  (r/with-let [close-burger #(reset! burger-active-atom false)
               _watch
               (add-watch state/route-match :navigation-event close-burger)]
    [:div {:class "z-[201] text-right flex flex-col"}
     [main-navigation-menu]
     [:div {:class "h-5"}]
     [login-controls]]
    (finally
      (remove-watch state/route-match :navigation-event))))

(defn page-container []
  ; (get-route-matched-view)
  (let [auth-ready-atom @(r/track state/get-auth-ready-atom)
        current-user-atom @(r/track state/get-logged-in-user-atom)
        get-logged-in-user-must-reset-password-atom
        @(r/track state/get-logged-in-user-must-reset-password-atom)
        connectivity-atom @(r/track state/get-connectivity-atom)
        is-offline (not (:online @connectivity-atom))
        offline-bg-class (when is-offline " bg-pink-50/50 ")]
    (println "is-offline" is-offline)
    ; TODO BUG fix route-matched-view being rendered when not logged in
    [:<>
     [:div {:class (str "min-h-screen pb-8 pt-4 px-4 flex flex-col" offline-bg-class)}
      [:div {:class "grow flex flex-col gap-8"}
       [top-bar]
       (when @burger-active-atom
         [burger-menu])
       (if @auth-ready-atom
         (if @current-user-atom
           (if @get-logged-in-user-must-reset-password-atom
             [forms-login/must-reset-password-page]
             (get-route-matched-view))
           [forms-login/login-page])
         [loading-page])]]
     [opacity-screen]]))

(defn logout-page []
  (r/create-class
   {:display-name  "logout-page"
    :component-did-mount
    (fn [_this]
      (firebase/<logout-user!))
    :reagent-render (fn [] [:span "Atsijungiama."])}))

#_(defn repeat-side-effect [n-ms side-effect-fn]
    (js/setInterval side-effect-fn n-ms))

#_(defn a-fn []
    (r/with-let [b (atom 0)
                 _ (add-watch
                    b :w1
                    (fn [_ _ _old new] (println "b" new)))]
      (let [a (r/atom 1)
            _ (add-watch
               a :w1
               (fn [_ _ _old new] (println "a" new)))]

        (println "a-fn inside")
        (repeat-side-effect 5000 #(swap! a inc))
        (repeat-side-effect 1000 #(swap! b dec))
        (swap! b dec)
        a)
      (finally (println "finally b is" b))))

(defn testpage []
  (println "rendering testpage")
  (let [_ nil
        ; user-atom @(r/track state/get-current-user-atom)
        ; a @(r/track a-fn)
        a
        @(r/track
          firestore/get-doc-subscription-atom
          (firestore/document-ref state/user-col-ref "pom0DdGHG6FcXI3gqY4ArObfMMFj"))]
    (println "@a" @a)
    [:div (str @a)]))
